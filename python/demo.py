#!/usr/bin/env python3
"""
FPT University Agent - Clean Demo
Production-ready Intent Detection System
"""

import sys
import asyncio
import time
from pathlib import Path
from typing import List, Optional

# Add src to Python path
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

# Core imports
from core.domain.entities import DetectionContext, IntentRule, IntentResult
from infrastructure.intent_detection.rule_based import RuleBasedDetectorImpl
from infrastructure.intent_detection.rule_loader import ProductionRuleLoader
from infrastructure.caching.memory_cache import MemoryCacheService
from infrastructure.vector_stores.qdrant_store import QdrantVectorStore
from infrastructure.embeddings.openai_embeddings import OpenAIEmbeddingService
from core.application.services.hybrid_intent_service import HybridIntentDetectionService, HybridConfig
from shared.utils.text_processing import VietnameseTextProcessor
from shared.utils.metrics import MetricsCollectorImpl
from shared.types import DetectionMethod

def load_production_rules() -> List[IntentRule]:
    """Load production rules"""
    loader = ProductionRuleLoader()
    rules = loader.load_rules()

    metadata = loader.get_rules_metadata()
    if metadata and metadata.get('coverage_areas'):
        print(f"📊 Coverage: {', '.join(metadata['coverage_areas'])}")

    return rules

TEST_QUERIES = [
    # Tuition inquiries
    "Học phí FPT 2025 bao nhiêu tiền?",
    "Chi phí học ngành CNTT như thế nào?",
    "Tuition fee for AI program?",
    "Có học bổng không?",

    # Admission requirements
    "Điểm chuẩn FPT 2024 bao nhiêu?",
    "Yêu cầu đầu vào ngành IT như thế nào?",
    "Hồ sơ đăng ký cần gì?",

    # Program information
    "FPT có những ngành nào?",
    "What programs are available?",
    "Ngành AI có học không?",

    # Campus facilities
    "Campus FPT ở đâu?",
    "Thư viện mở cửa lúc mấy giờ?",
    "Ký túc xá giá bao nhiêu?",

    # Student services
    "Dịch vụ sinh viên có gì?",
    "Hỗ trợ tìm việc làm không?",

    # Technical support
    "Portal FAP bị lỗi làm sao?",
    "Quên mật khẩu email trường?",

    # Out of scope
    "Hôm nay trời có mưa không?",
    "Cách nấu phở bò Hà Nội?"
]


class SimpleIntentDetectionService:
    """Simplified intent detection service for demo"""
    
    def __init__(self, rule_detector, cache_service, text_processor, metrics_collector):
        self.rule_detector = rule_detector
        self.cache_service = cache_service
        self.text_processor = text_processor
        self.metrics_collector = metrics_collector
    
    async def detect(self, context: DetectionContext) -> Result[IntentResult]:
        """Detect intent using rule-based approach"""
        start_time = time.time()
        query = context.query
        
        self.metrics_collector.increment_counter("intent_detection_requests")
        
        try:
            # Check for irrelevant queries
            if self.text_processor.is_irrelevant_query(query):
                result = IntentResult(
                    id="general_info",
                    confidence=0.1,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "irrelevant_query"}
                )
                processing_time = (time.time() - start_time) * 1000
                result = result.with_metadata(processing_time_ms=processing_time)
                return Result.ok(result)
            
            # Check cache
            cache_key = self._get_cache_key(query)
            cached_result = await self.cache_service.get(cache_key)
            if cached_result:
                self.metrics_collector.increment_counter("cache_hits")
                return Result.ok(IntentResult(**cached_result))
            
            self.metrics_collector.increment_counter("cache_misses")
            
            # Rule-based detection
            rule_match = await self.rule_detector.detect(query)
            
            if rule_match:
                result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns,
                        "rule_weight": rule_match.weight
                    }
                )
            else:
                result = IntentResult(
                    id="general_info",
                    confidence=0.2,
                    method=DetectionMethod.FALLBACK,
                    metadata={"reason": "no_rule_match"}
                )
            
            processing_time = (time.time() - start_time) * 1000
            result = result.with_metadata(processing_time_ms=processing_time)
            
            # Cache result
            if result.confidence >= 0.3:
                cache_data = {
                    "id": result.id,
                    "confidence": result.confidence,
                    "method": result.method,
                    "metadata": result.metadata,
                    "timestamp": result.timestamp.isoformat()
                }
                await self.cache_service.set(cache_key, cache_data, ttl_seconds=300)
            
            self.metrics_collector.increment_counter("intent_detection_success")
            return Result.ok(result)
            
        except Exception as e:
            self.metrics_collector.increment_counter("intent_detection_errors")
            return Result.error(str(e), "DETECTION_ERROR")
    
    def _get_cache_key(self, query: str) -> str:
        import hashlib
        return f"intent:{hashlib.md5(query.encode()).hexdigest()}"
    
    async def get_performance_metrics(self):
        return self.metrics_collector.get_all_metrics()


async def run_demo(mode="full"):
    """Run intent detection demo"""

    print("🌸 FPT University Agent - Refactored Demo")
    print("=" * 50)

    if mode == "simple":
        print("Running simple functionality test...")
        return await run_simple_test()
    elif mode == "hybrid":
        print("Running hybrid demo with vector search...")
        return await run_hybrid_demo()
    
    # Initialize components
    print("🔧 Initializing components...")
    
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    cache_service = MemoryCacheService(max_size=1000, default_ttl=300)
    # Load production rules
    rules = load_rules(use_production=True)
    rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)
    
    intent_service = SimpleIntentDetectionService(
        rule_detector=rule_detector,
        cache_service=cache_service,
        text_processor=text_processor,
        metrics_collector=metrics_collector
    )
    
    print("✅ Components initialized successfully!")
    print()
    
    # Test queries
    print("🎯 Testing intent detection...")
    print("-" * 30)
    
    for i, query in enumerate(TEST_QUERIES, 1):
        print(f"\n{i}. Query: '{query}'")
        
        context = DetectionContext(
            query=query,
            user_id="demo_user",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )
        
        start_time = time.time()
        result = await intent_service.detect(context)
        duration_ms = (time.time() - start_time) * 1000
        
        if result.is_ok():
            intent_result = result.data
            confidence_icon = "🟢" if intent_result.confidence >= 0.7 else "🟡" if intent_result.confidence >= 0.5 else "🔴"
            
            print(f"   {confidence_icon} Intent: {intent_result.id}")
            print(f"   📊 Confidence: {intent_result.confidence:.3f}")
            print(f"   🔧 Method: {intent_result.method}")
            print(f"   ⏱️ Duration: {duration_ms:.1f}ms")
            
            if "matched_keywords" in intent_result.metadata:
                print(f"   🔑 Keywords: {intent_result.metadata['matched_keywords']}")
        else:
            print(f"   ❌ Error: {result.error}")
        
        await asyncio.sleep(0.05)
    
    # Performance metrics
    print("\n📊 Performance Summary:")
    print("-" * 20)
    
    metrics = await intent_service.get_performance_metrics()
    counters = metrics.get('counters', {})
    
    print(f"Total requests: {counters.get('intent_detection_requests', 0)}")
    print(f"Cache hits: {counters.get('cache_hits', 0)}")
    print(f"Cache misses: {counters.get('cache_misses', 0)}")
    print(f"Successful detections: {counters.get('intent_detection_success', 0)}")
    print(f"Errors: {counters.get('intent_detection_errors', 0)}")
    
    cache_stats = await cache_service.get_stats()
    print(f"Cache hit rate: {cache_stats.get('hit_rate', 0):.1f}%")
    
    print("\n🎉 Demo completed successfully!")
    print("✅ Clean Architecture intent detection is working!")


async def run_hybrid_demo():
    """Run hybrid demo with vector search and indexing"""
    print("🔧 Initializing hybrid components...")

    # Initialize basic components
    text_processor = VietnameseTextProcessor()
    metrics_collector = MetricsCollectorImpl()
    cache_service = MemoryCacheService(max_size=1000, default_ttl=300)

    # Load production rules
    rules = load_rules(use_production=True)
    rule_detector = RuleBasedDetectorImpl(rules=rules, text_processor=text_processor)

    # Initialize vector components (optional - will gracefully degrade if not available)
    try:
        vector_store = QdrantVectorStore()
        embedding_service = OpenAIEmbeddingService(metrics_collector=metrics_collector)

        if vector_store.available and embedding_service.available:
            print("✅ Vector search components available")

            # Initialize hybrid service
            hybrid_config = HybridConfig(
                rule_high_confidence_threshold=0.7,
                vector_confidence_threshold=0.6,
                early_exit_threshold=0.9
            )

            hybrid_service = HybridIntentDetectionService(
                rule_detector=rule_detector,
                vector_store=vector_store,
                embedding_service=embedding_service,
                cache_service=cache_service,
                text_processor=text_processor,
                metrics_collector=metrics_collector,
                config=hybrid_config
            )

            # Optional: Index examples if needed
            print("🔄 Checking vector index...")
            collection_info = await vector_store.get_collection_info()

            if collection_info.get("points_count", 0) == 0:
                print("📚 Vector store is empty, would need indexing...")
                print("   (Run indexing separately with proper intent examples)")
            else:
                print(f"✅ Vector store has {collection_info.get('points_count', 0)} indexed examples")

        else:
            print("⚠️ Vector search not available, using rule-based only")
            hybrid_service = None

    except Exception as e:
        print(f"⚠️ Vector components initialization failed: {e}")
        print("🔄 Falling back to rule-based detection only")
        hybrid_service = None

    print("✅ Hybrid components initialized!")

    # Test queries
    print("\n🎯 Testing hybrid intent detection...")
    print("-" * 40)

    test_queries = [
        "Học phí FPT 2025 bao nhiêu tiền?",
        "Tuition fee for AI program?",
        "Campus FPT ở đâu?",
        "Portal FAP bị lỗi làm sao?",
        "Hôm nay trời có mưa không?"  # Irrelevant
    ]

    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Query: '{query}'")

        context = DetectionContext(
            query=query,
            user_id="hybrid_demo_user",
            language="vi" if any(ord(c) > 127 for c in query) else "en"
        )

        start_time = time.time()

        if hybrid_service:
            # Use hybrid detection
            intent_result = await hybrid_service.detect_intent(context)
        else:
            # Fallback to rule-based only
            rule_match = await rule_detector.detect(query)
            if rule_match:
                intent_result = IntentResult(
                    id=rule_match.intent_id,
                    confidence=rule_match.score,
                    method=DetectionMethod.RULE,
                    metadata={
                        "matched_keywords": rule_match.matched_keywords,
                        "matched_patterns": rule_match.matched_patterns
                    }
                )
            else:
                intent_result = IntentResult(
                    id="general_info",
                    confidence=0.2,
                    method=DetectionMethod.FALLBACK
                )

        duration_ms = (time.time() - start_time) * 1000

        # Display results
        confidence_icon = "🟢" if intent_result.confidence >= 0.7 else "🟡" if intent_result.confidence >= 0.5 else "🔴"
        method_icon = {
            DetectionMethod.RULE: "📏",
            DetectionMethod.VECTOR: "🔍",
            DetectionMethod.HYBRID: "🔀",
            DetectionMethod.FALLBACK: "🔄"
        }.get(intent_result.method, "❓")

        print(f"   {confidence_icon} Intent: {intent_result.id}")
        print(f"   📊 Confidence: {intent_result.confidence:.3f}")
        print(f"   {method_icon} Method: {intent_result.method}")
        print(f"   ⏱️ Duration: {duration_ms:.1f}ms")

        if intent_result.metadata.get("matched_keywords"):
            print(f"   🔑 Keywords: {intent_result.metadata['matched_keywords']}")

        await asyncio.sleep(0.1)

    # Performance summary
    if hybrid_service:
        print("\n📊 Hybrid Performance Summary:")
        print("-" * 30)

        stats = await hybrid_service.get_performance_stats()

        if "counters" in stats:
            counters = stats["counters"]
            print(f"Rule detections: {counters.get('rule_detections', 0)}")
            print(f"Vector searches: {counters.get('vector_searches', 0)}")
            print(f"Cache hits: {counters.get('cache_hits', 0)}")

        if "cache" in stats:
            cache_info = stats["cache"]
            print(f"Cache hit rate: {cache_info.get('hit_rate', 0):.1f}%")

        if "vector_store" in stats:
            vector_info = stats["vector_store"]
            if vector_info.get("available"):
                print(f"Vector store: {vector_info.get('points_count', 0)} indexed examples")

    print("\n🎉 Hybrid demo completed!")
    print("✅ Hybrid intent detection system ready!")


async def run_simple_test():
    """Run simple functionality test"""
    
    try:
        # Test imports
        from src.core.domain.entities import IntentResult, DetectionContext
        from src.shared.utils.text_processing import VietnameseTextProcessor
        print("✅ Core imports successful")
        
        # Test text processor
        processor = VietnameseTextProcessor()
        normalized = processor.normalize_vietnamese("Học phí FPT")
        print(f"✅ Text processing: '{normalized}'")
        
        # Test entities
        result = IntentResult(
            id="test_intent",
            confidence=0.95,
            method=DetectionMethod.RULE
        )
        print(f"✅ Entity creation: {result.id} ({result.confidence})")
        
        print("\n🎉 Simple test passed! System is ready.")
        return True
        
    except Exception as e:
        print(f"❌ Simple test failed: {e}")
        return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="FPT University Agent Demo")
    parser.add_argument(
        "--mode",
        choices=["simple", "full", "hybrid"],
        default="full",
        help="Demo mode: simple (basic test), full (rule-based), or hybrid (with vector search)"
    )
    
    args = parser.parse_args()
    
    try:
        asyncio.run(run_demo(args.mode))
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
